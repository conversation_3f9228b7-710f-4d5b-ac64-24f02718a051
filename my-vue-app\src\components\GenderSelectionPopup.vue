<template>
  <div class="gender-popup-overlay" @click="closePopup">
    <div class="gender-popup" @click.stop>
      <!-- 男性选项 -->
      <div class="gender-option male-option" @click="selectGender('male')">
        <div class="option-content">
          <img src="@/assets/images/male-icon.svg" alt="男" class="gender-icon" />
          <span class="gender-text">男</span>
        </div>
        <div class="option-divider"></div>
      </div>

      <!-- 女性选项 -->
      <div class="gender-option female-option" @click="selectGender('female')">
        <div class="option-content">
          <img src="@/assets/images/female-icon.svg" alt="女" class="gender-icon" />
          <span class="gender-text">女</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'GenderSelectionPopup',
  emits: ['close', 'select-gender'],
  methods: {
    closePopup() {
      this.$emit('close')
    },
    selectGender(gender) {
      this.$emit('select-gender', gender)
      this.closePopup()
    }
  }
}
</script>

<style scoped>
.gender-popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.gender-popup {
  width: 374px;
  height: 136px;
  background: #FFFFFF;
  border-radius: 16px;
  position: relative;
  animation: fadeIn 0.3s ease-out;
  box-shadow: 0px 4px 20px rgba(0, 0, 0, 0.15);
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.gender-option {
  width: 100%;
  height: 60px;
  position: relative;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #FFFFFF;
}

.male-option {
  position: absolute;
  top: 8px;
  left: 0;
  width: 374px;
  height: 60px;
}

.female-option {
  position: absolute;
  top: 68px;
  left: 0;
  width: 374px;
  height: 60px;
}

.option-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  width: 100%;
  height: 100%;
}

.gender-icon {
  width: 24px;
  height: 24px;
}

.gender-text {
  font-family: Inter;
  font-weight: 400;
  font-size: 16px;
  line-height: 26px;
  color: #171A1F;
}

.option-divider {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 1px;
  background: #DEE1E6;
}

.gender-option:hover {
  background: #F8F9FA;
}

.gender-option:active {
  background: #F0F0F0;
}
</style>
