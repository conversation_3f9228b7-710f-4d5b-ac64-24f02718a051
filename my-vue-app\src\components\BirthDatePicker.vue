<template>
  <div class="birth-date-overlay" @click="closePopup">
    <div class="birth-date-picker" @click.stop>
      <!-- 标题行 -->
      <div class="picker-header">
        <div class="header-item">
          <span class="header-text">年份</span>
        </div>
        <div class="header-item">
          <span class="header-text">月份</span>
        </div>
        <div class="header-item">
          <span class="header-text">日期</span>
        </div>
      </div>

      <!-- 分隔线 -->
      <div class="header-divider"></div>

      <!-- 滚动选择区域 -->
      <div class="picker-content">
        <!-- 年份列 -->
        <div class="picker-column">
          <div 
            v-for="year in years" 
            :key="year"
            class="picker-item"
            :class="{ active: selectedYear === year }"
            @click="selectYear(year)"
          >
            {{ year }}
          </div>
        </div>

        <!-- 月份列 -->
        <div class="picker-column">
          <div 
            v-for="month in months" 
            :key="month"
            class="picker-item"
            :class="{ active: selectedMonth === month }"
            @click="selectMonth(month)"
          >
            {{ month }}
          </div>
        </div>

        <!-- 日期列 -->
        <div class="picker-column">
          <div 
            v-for="day in days" 
            :key="day"
            class="picker-item"
            :class="{ active: selectedDay === day }"
            @click="selectDay(day)"
          >
            {{ String(day).padStart(2, '0') }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'BirthDatePicker',
  emits: ['close', 'select-date'],
  data() {
    return {
      selectedYear: 2021,
      selectedMonth: 7,
      selectedDay: 7,
      years: [],
      months: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12],
      days: []
    }
  },
  mounted() {
    this.generateYears()
    this.generateDays()
  },
  watch: {
    selectedYear() {
      this.generateDays()
    },
    selectedMonth() {
      this.generateDays()
    }
  },
  methods: {
    generateYears() {
      const currentYear = new Date().getFullYear()
      for (let year = currentYear - 50; year <= currentYear + 10; year++) {
        this.years.push(year)
      }
    },
    generateDays() {
      const daysInMonth = new Date(this.selectedYear, this.selectedMonth, 0).getDate()
      this.days = []
      for (let day = 1; day <= daysInMonth; day++) {
        this.days.push(day)
      }
      // 如果当前选择的日期超过了该月的天数，调整为该月最后一天
      if (this.selectedDay > daysInMonth) {
        this.selectedDay = daysInMonth
      }
    },
    selectYear(year) {
      this.selectedYear = year
      this.emitDate()
    },
    selectMonth(month) {
      this.selectedMonth = month
      this.emitDate()
    },
    selectDay(day) {
      this.selectedDay = day
      this.emitDate()
    },
    emitDate() {
      const date = {
        year: this.selectedYear,
        month: this.selectedMonth,
        day: this.selectedDay,
        formatted: `${this.selectedYear}-${String(this.selectedMonth).padStart(2, '0')}-${String(this.selectedDay).padStart(2, '0')}`
      }
      this.$emit('select-date', date)
    },
    closePopup() {
      this.$emit('close')
    }
  }
}
</script>

<style scoped>
.birth-date-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.birth-date-picker {
  width: 374px;
  height: 423px;
  background: #FFFFFF;
  border-radius: 16px;
  position: relative;
  animation: fadeIn 0.3s ease-out;
  box-shadow: 0px 4px 20px rgba(0, 0, 0, 0.15);
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.picker-header {
  position: absolute;
  top: 1px;
  left: 28px;
  width: 318px;
  height: 58px;
  display: flex;
}

.header-item {
  width: 106px;
  height: 58px;
  background: #FFFFFF;
  display: flex;
  align-items: center;
  justify-content: center;
}

.header-text {
  font-family: Inter;
  font-weight: 400;
  font-size: 16px;
  line-height: 26px;
  color: #000000;
  text-align: center;
}

.header-divider {
  position: absolute;
  top: 59px;
  left: 31px;
  width: 312px;
  height: 1px;
  background: #DEE1E6;
}

.picker-content {
  position: absolute;
  top: 60px;
  left: 28px;
  width: 318px;
  height: 363px;
  display: flex;
  overflow: hidden;
}

.picker-column {
  width: 106px;
  height: 100%;
  overflow-y: auto;
  scroll-behavior: smooth;
}

.picker-column::-webkit-scrollbar {
  width: 0;
}

.picker-item {
  width: 106px;
  height: 52px;
  background: #FFFFFF;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-family: Inter;
  font-weight: 400;
  font-size: 12px;
  line-height: 20px;
  color: #6E7787;
  text-align: center;
  transition: all 0.2s ease;
}

.picker-item:hover {
  background: #F8F9FA;
}

.picker-item.active {
  color: #323842;
  font-weight: 500;
}
</style>
