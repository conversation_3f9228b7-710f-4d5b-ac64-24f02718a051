<template>
  <div class="verification-page">
    <!-- 状态栏 -->
    <div class="status-bar">
      <div class="status-left">
        <div class="signal-container">
          <img :src="signalIcon" alt="信号" class="signal-icon" />
          <img :src="signalBar1" alt="信号条1" class="signal-bar" />
          <img :src="signalBar2" alt="信号条2" class="signal-bar" />
          <img :src="signalBar3" alt="信号条3" class="signal-bar" />
        </div>
      </div>
      <div class="status-right">
        <div class="battery-container">
          <img :src="batteryOutline" alt="电池外框" class="battery-outline" />
          <img :src="batteryFill1" alt="电池电量" class="battery-fill" />
          <img :src="batteryFill2" alt="电池电量" class="battery-fill" />
          <img :src="batteryFill3" alt="电池电量" class="battery-fill" />
          <img :src="batteryFill4" alt="电池电量" class="battery-fill" />
          <img :src="batteryFill5" alt="电池电量" class="battery-fill" />
          <img :src="batteryFill6" alt="电池电量" class="battery-fill" />
          <img :src="batteryFill7" alt="电池电量" class="battery-fill" />
        </div>
      </div>
    </div>

    <!-- 返回按钮 -->
    <div class="back-button" @click="goBack">
      <img :src="backButtonVerification" alt="返回" class="back-icon" />
    </div>

    <!-- 手机验证图标 -->
    <div class="phone-icon">
      <img :src="phoneVerificationIcon" alt="手机验证" class="phone-verification-icon" />
    </div>

    <!-- 标题 -->
    <div class="title">验证您的手机号码</div>

    <!-- 描述文字 -->
    <div class="description">
      请输入我们发送到您新手机号码的验证码<br>
      以完成绑定过程
    </div>

    <!-- 验证码输入框 -->
    <div class="verification-inputs">
      <div class="input-box" v-for="(digit, index) in verificationCode" :key="index">
        <input 
          type="text" 
          maxlength="1" 
          v-model="verificationCode[index]"
          @input="handleInput(index, $event)"
          @keydown="handleKeydown(index, $event)"
          :ref="`input-${index}`"
          class="digit-input"
        />
      </div>
    </div>

    <!-- 重新发送验证码按钮 -->
    <div class="resend-button" @click="resendCode">
      <span>重新发送验证码</span>
    </div>
  </div>
</template>

<script>
// 导入图片资源
import signalIcon from '../assets/images/signal-icon.svg'
import signalBar1 from '../assets/images/signal-bar1.svg'
import signalBar2 from '../assets/images/signal-bar2.svg'
import signalBar3 from '../assets/images/signal-bar3.svg'
import batteryOutline from '../assets/images/battery-outline.svg'
import batteryFill1 from '../assets/images/battery-fill1.svg'
import batteryFill2 from '../assets/images/battery-fill2.svg'
import batteryFill3 from '../assets/images/battery-fill3.svg'
import batteryFill4 from '../assets/images/battery-fill4.svg'
import batteryFill5 from '../assets/images/battery-fill5.svg'
import batteryFill6 from '../assets/images/battery-fill6.svg'
import batteryFill7 from '../assets/images/battery-fill7.svg'
import backButtonVerification from '../assets/images/back-button-verification.svg'
import phoneVerificationIcon from '../assets/images/phone-verification-icon.svg'

export default {
  name: 'VerificationCodePage',
  emits: ['go-back', 'verification-complete', 'resend-code'],
  data() {
    return {
      // 状态栏图标
      signalIcon,
      signalBar1,
      signalBar2,
      signalBar3,
      batteryOutline,
      batteryFill1,
      batteryFill2,
      batteryFill3,
      batteryFill4,
      batteryFill5,
      batteryFill6,
      batteryFill7,
      // 页面图标
      backButtonVerification,
      phoneVerificationIcon,
      // 验证码
      verificationCode: ['', '', '', '', '', '']
    }
  },
  methods: {
    goBack() {
      this.$emit('go-back')
    },
    resendCode() {
      this.$emit('resend-code')
    },
    handleInput(index, event) {
      const value = event.target.value
      if (value && index < 5) {
        // 自动跳转到下一个输入框
        this.$nextTick(() => {
          const nextInput = this.$refs[`input-${index + 1}`]
          if (nextInput && nextInput[0]) {
            nextInput[0].focus()
          }
        })
      }
      
      // 检查是否所有输入框都已填写
      this.checkComplete()
    },
    handleKeydown(index, event) {
      if (event.key === 'Backspace' && !this.verificationCode[index] && index > 0) {
        // 如果当前输入框为空且按下退格键，跳转到前一个输入框
        this.$nextTick(() => {
          const prevInput = this.$refs[`input-${index - 1}`]
          if (prevInput && prevInput[0]) {
            prevInput[0].focus()
          }
        })
      }
    },
    checkComplete() {
      const code = this.verificationCode.join('')
      if (code.length === 6) {
        this.$emit('verification-complete', code)
      }
    }
  }
}
</script>

<style scoped>
.verification-page {
  width: 390px;
  height: 844px;
  background: #FFFFFF;
  position: relative;
  margin: 0 auto;
  box-shadow: 0px 3px 6px 0px rgba(18, 15, 40, 0.12);
  font-family: 'Inter', sans-serif;
  overflow-y: auto;
}

/* 状态栏 */
.status-bar {
  width: 100%;
  height: 40px;
  background: transparent;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 12px;
  box-sizing: border-box;
  border-bottom: 1px solid #F3F4F6;
}

.status-left {
  display: flex;
  align-items: center;
}

.signal-container {
  position: relative;
  width: 27.34px;
  height: 10.7px;
}

.signal-icon {
  position: absolute;
  left: 0;
  top: 0;
  width: 7.86px;
  height: 10.7px;
}

.signal-bar {
  position: absolute;
}

.signal-bar:nth-child(2) {
  left: 9.59px;
  top: 1.61px;
  width: 2.25px;
  height: 7.47px;
}

.signal-bar:nth-child(3) {
  left: 13.53px;
  top: 0.25px;
  width: 8.1px;
  height: 10.19px;
}

.signal-bar:nth-child(4) {
  left: 22.86px;
  top: 0.25px;
  width: 4.48px;
  height: 10.19px;
}

.status-right {
  display: flex;
  align-items: center;
}

.battery-container {
  position: relative;
  width: 65.87px;
  height: 10.56px;
}

.battery-outline {
  position: absolute;
  left: 43.05px;
  top: 0;
  width: 20.28px;
  height: 10.06px;
  opacity: 0.35;
}

.battery-fill {
  position: absolute;
}

.battery-fill:nth-child(2) {
  left: 64.68px;
  top: 3.75px;
  width: 1.19px;
  height: 3.59px;
  opacity: 0.4;
}

.battery-fill:nth-child(3) {
  left: 44.26px;
  top: 1.2px;
  width: 17.87px;
  height: 7.66px;
}

.battery-fill:nth-child(4) {
  left: 22.13px;
  top: 0.35px;
  width: 14.47px;
  height: 10.07px;
}

.battery-fill:nth-child(5) {
  left: 8.51px;
  top: 2.05px;
  width: 2.55px;
  height: 8.51px;
}

.battery-fill:nth-child(6) {
  left: 12.77px;
  top: 0.35px;
  width: 2.55px;
  height: 10.21px;
}

.battery-fill:nth-child(7) {
  left: 4.26px;
  top: 5.03px;
  width: 2.55px;
  height: 5.53px;
}

.battery-fill:nth-child(8) {
  left: 0;
  top: 7.16px;
  width: 2.55px;
  height: 3.4px;
}

/* 返回按钮 */
.back-button {
  position: absolute;
  left: 28px;
  top: 64px;
  width: 36px;
  height: 36px;
  cursor: pointer;
  border-radius: 18px;
}

.back-icon {
  width: 100%;
  height: 100%;
}

/* 手机验证图标 */
.phone-icon {
  position: absolute;
  left: 115px;
  top: 143px;
  width: 160px;
  height: 160px;
}

.phone-verification-icon {
  width: 100%;
  height: 100%;
}

/* 标题 */
.title {
  position: absolute;
  left: 67px;
  top: 311px;
  width: 256px;
  height: 48px;
  font-family: Archivo;
  font-weight: 700;
  font-size: 32px;
  line-height: 48px;
  text-align: center;
  color: #171A1F;
}

/* 描述文字 */
.description {
  position: absolute;
  left: 38px;
  top: 375px;
  width: 314px;
  height: 44px;
  font-family: Inter;
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
  text-align: center;
  color: #9095A0;
}

/* 验证码输入框 */
.verification-inputs {
  position: absolute;
  left: 45px;
  top: 473px;
  display: flex;
  gap: 12px;
}

.input-box {
  width: 40px;
  height: 40px;
  border: 1px solid #9095A0;
  border-radius: 6px;
  background: #FFFFFF;
  display: flex;
  align-items: center;
  justify-content: center;
}

.digit-input {
  width: 100%;
  height: 100%;
  border: none;
  outline: none;
  text-align: center;
  font-family: Inter;
  font-weight: 400;
  font-size: 18px;
  color: #171A1F;
  background: transparent;
}

/* 重新发送验证码按钮 */
.resend-button {
  position: absolute;
  left: 28px;
  top: 726px;
  width: 335px;
  height: 44px;
  background: transparent;
  border: 1px solid #171A1F;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.resend-button span {
  font-family: Inter;
  font-weight: 400;
  font-size: 16px;
  line-height: 26px;
  color: #171A1F;
}
</style>
