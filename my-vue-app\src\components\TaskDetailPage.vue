<template>
  <div class="task-detail-page">
    <!-- 顶部容器 -->
    <div class="top-container">
      <!-- 左侧图标 -->
      <div class="left-icon-container">
        <img :src="taskDetailIcon1" alt="任务图标1" class="task-detail-icon-1" />
      </div>
      
      <!-- 右侧图标 -->
      <div class="right-icon-container">
        <img :src="taskDetailIcon2" alt="任务图标2" class="task-detail-icon-2" />
      </div>
    </div>

    <!-- 返回按钮 -->
    <div class="back-button" @click="goBack">
      <img :src="arrowLeft" alt="返回" class="arrow-left" />
    </div>

    <!-- 分隔条 -->
    <div class="separator"></div>

    <!-- 页面标题 -->
    <div class="page-title">新任务</div>

    <!-- 日期时间信息 -->
    <div class="date-time-group">
      <div class="date-text">2024.12.22</div>
      <div class="day-text">星期一</div>
      <div class="time-text">10:22</div>
    </div>

    <!-- 任务描述 -->
    <div class="task-description">老师布置了作文任务，快去查看吧！～</div>
  </div>
</template>

<script>
import taskDetailIcon1 from '../assets/images/task-detail-icon-1.svg'
import taskDetailIcon2 from '../assets/images/task-detail-icon-2.svg'
import arrowLeft from '../assets/images/task-detail-arrow-left.svg'

export default {
  name: 'TaskDetailPage',
  emits: ['go-back'],
  data() {
    return {
      taskDetailIcon1,
      taskDetailIcon2,
      arrowLeft
    }
  },
  methods: {
    goBack() {
      this.$emit('go-back')
    }
  }
}
</script>

<style scoped>
.task-detail-page {
  width: 390px;
  height: 844px;
  background: #FFFFFF;
  box-shadow: 0px 3px 6px 0px rgba(18, 15, 40, 0.12);
  position: relative;
  margin: 0 auto;
}

.top-container {
  width: 390px;
  height: 40px;
  border-bottom: 1px solid #F3F4F6;
  position: relative;
}

.left-icon-container {
  width: 72px;
  height: 40px;
  border-right: 1px solid #BCC1CA;
  position: absolute;
  left: 0;
  top: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.task-detail-icon-1 {
  width: 27.34px;
  height: 10.7px;
}

.right-icon-container {
  width: 96px;
  height: 40px;
  border-left: 1px solid #BCC1CA;
  position: absolute;
  right: 0;
  top: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.task-detail-icon-2 {
  width: 65.87px;
  height: 10.56px;
}

.back-button {
  width: 24px;
  height: 24px;
  position: absolute;
  left: 24px;
  top: 48px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.arrow-left {
  width: 17.2px;
  height: 12.04px;
}

.separator {
  width: 375px;
  height: 4px;
  background: #F8F9FA;
  position: absolute;
  left: 0;
  top: 80px;
}

.page-title {
  position: absolute;
  left: 160px;
  top: 96px;
  width: 48px;
  height: 26px;
  font-family: Inter;
  font-weight: 400;
  font-size: 16px;
  line-height: 26px;
  color: #323842;
}

.date-time-group {
  position: absolute;
  left: 107px;
  top: 130px;
  width: 154px;
  height: 20px;
}

.date-text {
  position: absolute;
  left: 0;
  top: 0;
  width: 63px;
  height: 20px;
  font-family: Inter;
  font-weight: 400;
  font-size: 12px;
  line-height: 20px;
  text-align: right;
  color: #9095A0;
}

.day-text {
  position: absolute;
  left: 75px;
  top: 0;
  width: 36px;
  height: 20px;
  font-family: Inter;
  font-weight: 400;
  font-size: 12px;
  line-height: 20px;
  text-align: right;
  color: #9095A0;
}

.time-text {
  position: absolute;
  left: 123px;
  top: 0;
  width: 31px;
  height: 20px;
  font-family: Inter;
  font-weight: 400;
  font-size: 12px;
  line-height: 20px;
  text-align: right;
  color: #9095A0;
}

.task-description {
  position: absolute;
  left: 36px;
  top: 178px;
  width: 204px;
  height: 20px;
  font-family: Inter;
  font-weight: 400;
  font-size: 12px;
  line-height: 20px;
  color: #323842;
}
</style>
