<template>
  <div class="relation-overlay" @click="closePopup">
    <div class="relation-picker" @click.stop>
      <!-- 标题行 -->
      <div class="picker-header">
        <div class="header-item">
          <span class="header-text">类型</span>
        </div>
        <div class="header-item">
          <span class="header-text">身份</span>
        </div>
      </div>

      <!-- 分隔线 -->
      <div class="header-divider"></div>

      <!-- 选择区域 -->
      <div class="picker-content">
        <!-- 类型列 -->
        <div class="type-column">
          <div
            class="type-item"
            :class="{ active: selectedType === 'adult' }"
            @click="selectType('adult')"
          >
            <span class="type-text">成人</span>
          </div>
          <div
            class="type-item"
            :class="{ active: selectedType === 'student' }"
            @click="selectType('student')"
          >
            <span class="type-text">学生</span>
          </div>
        </div>

        <!-- 身份列 -->
        <div class="identity-column">
          <!-- 成人身份 -->
          <div v-if="selectedType === 'adult'" class="identity-section">
            <div
              v-for="identity in adultIdentities"
              :key="identity.id"
              class="identity-item"
              :class="{ active: selectedRelation === identity.id }"
              @click="selectRelation(identity.id, identity.label)"
            >
              {{ identity.label }}
            </div>
          </div>

          <!-- 学生身份 -->
          <div v-if="selectedType === 'student'" class="identity-section">
            <div
              v-for="identity in studentIdentities"
              :key="identity.id"
              class="identity-item"
              :class="{ active: selectedRelation === identity.id }"
              @click="selectRelation(identity.id, identity.label)"
            >
              {{ identity.label }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'MemberRelationPicker',
  emits: ['close', 'select-relation'],
  props: {
    currentRelation: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      selectedRelation: this.currentRelation || 'mom',
      selectedType: 'adult', // 默认选择成人类型
      adultIdentities: [
        { id: 'mom', label: '妈妈' },
        { id: 'dad', label: '爸爸' },
        { id: 'grandpa', label: '爷爷' },
        { id: 'grandma', label: '奶奶' },
        { id: 'grandfather', label: '外公' },
        { id: 'grandmother', label: '外婆' }
      ],
      studentIdentities: [
        { id: 'baby', label: '宝贝' },
        { id: 'brother', label: '哥哥' },
        { id: 'sister', label: '姐姐' },
        { id: 'younger-brother', label: '弟弟' },
        { id: 'younger-sister', label: '妹妹' }
      ]
    }
  },
  mounted() {
    // 根据当前选择的关系设置默认类型
    if (this.currentRelation) {
      this.selectedType = this.getRelationType(this.currentRelation)
    }
  },
  methods: {
    selectType(type) {
      this.selectedType = type
      // 切换类型时，清除当前选择的关系
      this.selectedRelation = ''
    },
    selectRelation(relationId, relationLabel) {
      this.selectedRelation = relationId
      const selection = {
        id: relationId,
        label: relationLabel,
        type: this.selectedType
      }
      this.$emit('select-relation', selection)
      this.closePopup()
    },
    getRelationType(relationId) {
      const adultRoles = ['mom', 'dad', 'grandpa', 'grandma', 'grandfather', 'grandmother']
      return adultRoles.includes(relationId) ? 'adult' : 'student'
    },
    closePopup() {
      this.$emit('close')
    }
  }
}
</script>

<style scoped>
.relation-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.relation-picker {
  width: 374px;
  height: 371px;
  background: #FFFFFF;
  border-radius: 16px;
  position: relative;
  animation: fadeIn 0.3s ease-out;
  box-shadow: 0px 4px 20px rgba(0, 0, 0, 0.15);
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.picker-header {
  position: absolute;
  top: 1px;
  left: 31px;
  width: 315px;
  height: 58px;
  display: flex;
}

.header-item {
  width: 158px;
  height: 58px;
  background: #FFFFFF;
  display: flex;
  align-items: center;
  justify-content: center;
}

.header-text {
  font-family: Inter;
  font-weight: 400;
  font-size: 16px;
  line-height: 26px;
  color: #000000;
  text-align: center;
}

.header-divider {
  position: absolute;
  top: 59px;
  left: 31px;
  width: 312px;
  height: 1px;
  background: #DEE1E6;
}

.picker-content {
  position: absolute;
  top: 60px;
  left: 31px;
  width: 315px;
  height: 311px;
  display: flex;
  overflow: hidden;
}

.type-column {
  width: 158px;
  height: 100%;
  background: #FFFFFF;
  display: flex;
  flex-direction: column;
}

.type-item {
  width: 158px;
  height: 52px;
  background: #FFFFFF;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  cursor: pointer;
  transition: all 0.2s ease;
}

.type-item:hover {
  background: #F8F9FA;
}

.type-item.active {
  background: #F0F0F0;
}

.type-text {
  font-family: Inter;
  font-weight: 400;
  font-size: 12px;
  line-height: 20px;
  color: #323842;
  text-align: center;
  transition: all 0.2s ease;
}

.type-item.active .type-text {
  color: #636AE8;
  font-weight: 500;
}

.identity-column {
  width: 158px;
  height: 100%;
  overflow-y: auto;
  scroll-behavior: smooth;
}

.identity-column::-webkit-scrollbar {
  width: 0;
}

.identity-section {
  display: flex;
  flex-direction: column;
}

.identity-item {
  width: 158px;
  height: 52px;
  background: #FFFFFF;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-family: Inter;
  font-weight: 400;
  font-size: 12px;
  line-height: 20px;
  color: #6E7787;
  text-align: center;
  transition: all 0.2s ease;
}

.identity-item:hover {
  background: #F8F9FA;
}

.identity-item.active {
  color: #323842;
  font-weight: 500;
}

/* 不同身份的颜色 */
.identity-item:nth-child(1) { color: #6E7787; }
.identity-item:nth-child(2) { color: #565E6C; }
.identity-item:nth-child(3) { color: #424955; }
.identity-item:nth-child(4) { color: #323842; }
.identity-item:nth-child(5) { color: #424955; }
.identity-item:nth-child(6) { color: #565E6C; }

.identity-item.active {
  color: #323842 !important;
  font-weight: 500;
}
</style>
