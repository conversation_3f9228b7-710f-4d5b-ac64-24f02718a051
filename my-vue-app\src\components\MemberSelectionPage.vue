<template>
  <div class="member-selection-page">
    <!-- 状态栏 -->
    <div class="status-bar">
      <div class="status-left">
        <div class="signal-container">
          <img :src="avatarIcon1" alt="信号" class="signal-icon" />
          <img :src="avatarIcon2" alt="信号条1" class="signal-bar" />
          <img :src="avatarIcon3" alt="信号条2" class="signal-bar" />
          <img :src="avatarIcon4" alt="信号条3" class="signal-bar" />
        </div>
      </div>
      <div class="status-right">
        <div class="battery-container">
          <img :src="avatarIcon5" alt="电池外框" class="battery-outline" />
          <img :src="avatarIcon6" alt="电池电量" class="battery-fill" />
          <img :src="avatarIcon7" alt="电池电量" class="battery-fill" />
        </div>
      </div>
    </div>

    <!-- 返回按钮 -->
    <div class="back-button" @click="goBack">
      <div class="arrow-group">
        <div class="arrow-line"></div>
        <div class="arrow-head"></div>
      </div>
    </div>

    <!-- 分隔线 -->
    <div class="separator"></div>

    <!-- 页面标题 -->
    <div class="page-title">选择成员关系</div>

    <!-- 成人身份选择 -->
    <div class="section-title adult-section">成人</div>

    <!-- 成人标签 - 第一行 -->
    <div class="tag-item tag-mom" :class="{ active: selectedRole === 'mom' }" @click="selectRole('mom')">
      <div class="tag-frame">
        <span class="tag-text">妈妈</span>
      </div>
    </div>

    <div class="tag-item tag-dad" :class="{ active: selectedRole === 'dad' }" @click="selectRole('dad')">
      <div class="tag-frame">
        <span class="tag-text">爸爸</span>
      </div>
    </div>

    <div class="tag-item tag-grandpa" :class="{ active: selectedRole === 'grandpa' }" @click="selectRole('grandpa')">
      <div class="tag-frame">
        <span class="tag-text">爷爷</span>
      </div>
    </div>

    <div class="tag-item tag-grandma" :class="{ active: selectedRole === 'grandma' }" @click="selectRole('grandma')">
      <div class="tag-frame">
        <span class="tag-text">奶奶</span>
      </div>
    </div>

    <div class="tag-item tag-grandfather" :class="{ active: selectedRole === 'grandfather' }" @click="selectRole('grandfather')">
      <div class="tag-frame">
        <span class="tag-text">外公</span>
      </div>
    </div>

    <!-- 成人标签 - 第二行 -->
    <div class="tag-item tag-grandmother" :class="{ active: selectedRole === 'grandmother' }" @click="selectRole('grandmother')">
      <div class="tag-frame">
        <span class="tag-text">外婆</span>
      </div>
    </div>

    <!-- 学生身份选择 -->
    <div class="section-title student-section">学生</div>

    <!-- 学生标签 -->
    <div class="tag-item tag-baby" :class="{ active: selectedRole === 'baby' }" @click="selectRole('baby')">
      <div class="tag-frame">
        <span class="tag-text">宝贝</span>
      </div>
    </div>

    <div class="tag-item tag-brother" :class="{ active: selectedRole === 'brother' }" @click="selectRole('brother')">
      <div class="tag-frame">
        <span class="tag-text">哥哥</span>
      </div>
    </div>

    <div class="tag-item tag-sister" :class="{ active: selectedRole === 'sister' }" @click="selectRole('sister')">
      <div class="tag-frame">
        <span class="tag-text">姐姐</span>
      </div>
    </div>

    <div class="tag-item tag-younger-brother" :class="{ active: selectedRole === 'younger-brother' }" @click="selectRole('younger-brother')">
      <div class="tag-frame">
        <span class="tag-text">弟弟</span>
      </div>
    </div>

    <div class="tag-item tag-younger-sister" :class="{ active: selectedRole === 'younger-sister' }" @click="selectRole('younger-sister')">
      <div class="tag-frame">
        <span class="tag-text">妹妹</span>
      </div>
    </div>

    <!-- 底部按钮 -->
    <div class="bottom-buttons">
      <button class="btn-wechat" @click="wechatInvite">
        <img :src="wechatIcon" alt="微信" class="btn-icon" />
        <span>微信邀请</span>
      </button>
      
      <button class="btn-manual" @click="manualAdd">
        <img :src="userPlusIcon" alt="添加" class="btn-icon" />
        <span>手动添加</span>
      </button>
    </div>
  </div>
</template>

<script>
// 导入图片资源
import avatarIcon1 from '../assets/images/avatar-icon-1.svg'
import avatarIcon2 from '../assets/images/avatar-icon-2.svg'
import avatarIcon3 from '../assets/images/avatar-icon-3.svg'
import avatarIcon4 from '../assets/images/avatar-icon-4.svg'
import avatarIcon5 from '../assets/images/avatar-icon-5.svg'
import avatarIcon6 from '../assets/images/avatar-icon-6.svg'
import avatarIcon7 from '../assets/images/avatar-icon-7.svg'

import userPlusIcon from '../assets/images/user-plus-icon.svg'
import wechatIcon from '../assets/images/wechat-icon.svg'

export default {
  name: 'MemberSelectionPage',
  emits: ['go-back', 'member-selected', 'wechat-invite', 'manual-add'],
  data() {
    return {
      // 图片资源
      avatarIcon1,
      avatarIcon2,
      avatarIcon3,
      avatarIcon4,
      avatarIcon5,
      avatarIcon6,
      avatarIcon7,

      userPlusIcon,
      wechatIcon,
      
      // 选中的身份
      selectedRole: 'baby'
    }
  },
  methods: {
    goBack() {
      this.$emit('go-back')
    },
    selectRole(roleId) {
      this.selectedRole = roleId
    },
    wechatInvite() {
      this.$emit('wechat-invite', this.selectedRole)
    },
    manualAdd() {
      this.$emit('manual-add', this.selectedRole)
    }
  }
}
</script>

<style scoped>
.member-selection-page {
  width: 375px;
  height: 844px;
  background: #FFFFFF;
  position: relative;
  margin: 0 auto;
  box-shadow: 0px 3px 6px 0px rgba(18, 15, 40, 0.12);
  font-family: 'Inter', sans-serif;
  overflow-y: auto;
}

/* 状态栏 */
.status-bar {
  position: absolute;
  left: 0;
  top: 0;
  width: 375px;
  height: 40px;
  background: transparent;
}

.status-left {
  position: absolute;
  left: 0;
  top: 0;
  width: 72px;
  height: 40px;
}

.signal-container {
  position: absolute;
  left: 30px;
  top: 16.79px;
  width: 27.34px;
  height: 10.7px;
}

.signal-icon {
  position: absolute;
  left: 0;
  top: 0;
  width: 7.86px;
  height: 10.7px;
}

.signal-bar:nth-child(2) {
  position: absolute;
  left: 9.59px;
  top: 1.61px;
  width: 2.25px;
  height: 7.47px;
}

.signal-bar:nth-child(3) {
  position: absolute;
  left: 13.53px;
  top: 0.25px;
  width: 8.1px;
  height: 10.19px;
}

.signal-bar:nth-child(4) {
  position: absolute;
  left: 22.86px;
  top: 0.25px;
  width: 4.48px;
  height: 10.19px;
}

.status-right {
  position: absolute;
  right: 0;
  top: 0;
  width: 96px;
  height: 40px;
}

.battery-container {
  position: absolute;
  left: 12px;
  top: 16.67px;
  width: 65.87px;
  height: 10.56px;
}

.battery-outline {
  position: absolute;
  left: 43.05px;
  top: 0;
  width: 20.28px;
  height: 10.06px;
  opacity: 0.35;
  stroke: #000000;
  stroke-width: 1px;
}

.battery-fill:nth-child(2) {
  position: absolute;
  left: 64.68px;
  top: 3.75px;
  width: 1.19px;
  height: 3.59px;
  opacity: 0.4;
}

.battery-fill:nth-child(3) {
  position: absolute;
  left: 44.26px;
  top: 1.2px;
  width: 17.87px;
  height: 7.66px;
}

/* 返回按钮 */
.back-button {
  position: absolute;
  left: 24px;
  top: 48px;
  width: 24px;
  height: 24px;
  cursor: pointer;
}

.arrow-group {
  position: relative;
  width: 17.2px;
  height: 12.04px;
  margin: 5.98px 3.4px;
}

.arrow-line {
  position: absolute;
  left: 0;
  top: 6.02px;
  width: 17.2px;
  height: 0;
  border-top: 2.064px solid #171A1F;
}

.arrow-head {
  position: absolute;
  left: 0;
  top: 6.02px;
  width: 0;
  height: 0;
  border-right: 6px solid #171A1F;
  border-top: 6px solid transparent;
  border-bottom: 6px solid transparent;
  transform: translateY(-50%);
}

/* 分隔线 */
.separator {
  position: absolute;
  left: 0;
  top: 80px;
  width: 375px;
  height: 4px;
  background: #F8F9FA;
}

/* 页面标题 */
.page-title {
  position: absolute;
  left: 140px;
  top: 46px;
  width: 96px;
  height: 26px;
  font-family: Inter;
  font-weight: 400;
  font-size: 16px;
  line-height: 26px;
  color: #323842;
  text-align: center;
}

/* 分类标题 */
.section-title {
  position: absolute;
  left: 22px;
  font-family: Inter;
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
  color: #171A1F;
}

.adult-section {
  top: 100px;
}

.student-section {
  top: 242px;
}

/* 标签项 */
.tag-item {
  position: absolute;
  width: 60px;
  height: 28px;
  cursor: pointer;
}

.tag-frame {
  width: 100%;
  height: 100%;
  background: #F2F2FD;
  border-radius: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tag-item.active .tag-frame {
  background: #636AE8;
}

.tag-text {
  font-family: Inter;
  font-weight: 400;
  font-size: 12px;
  line-height: 20px;
  color: #636AE8;
}

.tag-item.active .tag-text {
  color: #FFFFFF;
}

/* 成人标签位置 - 第一行 */
.tag-mom {
  left: 22px;
  top: 138px;
}

.tag-dad {
  left: 90px;
  top: 138px;
}

.tag-grandpa {
  left: 158px;
  top: 138px;
}

.tag-grandma {
  left: 226px;
  top: 138px;
}

.tag-grandfather {
  left: 294px;
  top: 138px;
}

/* 成人标签位置 - 第二行 */
.tag-grandmother {
  left: 22px;
  top: 190px;
}

/* 学生标签位置 */
.tag-baby {
  left: 22px;
  top: 280px;
}

.tag-brother {
  left: 90px;
  top: 280px;
}

.tag-sister {
  left: 158px;
  top: 280px;
}

.tag-younger-brother {
  left: 226px;
  top: 280px;
}

.tag-younger-sister {
  left: 294px;
  top: 280px;
}

/* 底部按钮 */
.bottom-buttons {
  position: absolute;
  left: 13px;
  top: 651px;
  width: 350px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.btn-wechat,
.btn-manual {
  width: 100%;
  height: 52px;
  border-radius: 26px;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  cursor: pointer;
  font-family: Inter;
  font-weight: 400;
  font-size: 18px;
  line-height: 28px;
}

.btn-wechat {
  background: #22CCB2;
  color: #FFFFFF;
  box-shadow: 0px 4px 9px 0px rgba(34, 204, 178, 0.11), 0px 0px 2px 0px rgba(34, 204, 178, 0.12);
}

.btn-manual {
  background: #636AE8;
  color: #FFFFFF;
  box-shadow: 0px 4px 9px 0px rgba(99, 106, 232, 0.11), 0px 0px 2px 0px rgba(99, 106, 232, 0.12);
}

.btn-icon {
  width: 24px;
  height: 24px;
}
</style>
