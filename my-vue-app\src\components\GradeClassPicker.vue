<template>
  <div class="grade-class-overlay" @click="closePopup">
    <div class="grade-class-picker" @click.stop>
      <!-- 标题行 -->
      <div class="picker-header">
        <div class="header-item">
          <span class="header-text">年级</span>
        </div>
        <div class="header-item">
          <span class="header-text">班级</span>
        </div>
      </div>

      <!-- 分隔线 -->
      <div class="header-divider"></div>

      <!-- 滚动选择区域 -->
      <div class="picker-content">
        <!-- 年级列 -->
        <div class="picker-column">
          <div 
            v-for="grade in grades" 
            :key="grade.value"
            class="picker-item"
            :class="{ active: selectedGrade === grade.value }"
            @click="selectGrade(grade.value)"
          >
            {{ grade.label }}
          </div>
        </div>

        <!-- 班级列 -->
        <div class="picker-column">
          <div 
            v-for="classNum in classes" 
            :key="classNum"
            class="picker-item"
            :class="{ active: selectedClass === classNum }"
            @click="selectClass(classNum)"
          >
            {{ classNum }}班
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'GradeClassPicker',
  emits: ['close', 'select-grade-class'],
  data() {
    return {
      selectedGrade: 1,
      selectedClass: 1,
      grades: [
        { value: 1, label: '一年级' },
        { value: 2, label: '二年级' },
        { value: 3, label: '三年级' },
        { value: 4, label: '四年级' },
        { value: 5, label: '五年级' },
        { value: 6, label: '六年级' }
      ],
      classes: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]
    }
  },
  methods: {
    selectGrade(grade) {
      this.selectedGrade = grade
      this.emitSelection()
    },
    selectClass(classNum) {
      this.selectedClass = classNum
      this.emitSelection()
    },
    emitSelection() {
      const gradeLabel = this.grades.find(g => g.value === this.selectedGrade)?.label || ''
      const selection = {
        grade: this.selectedGrade,
        gradeLabel: gradeLabel,
        class: this.selectedClass,
        formatted: `${gradeLabel}${this.selectedClass}班`
      }
      this.$emit('select-grade-class', selection)
    },
    closePopup() {
      this.$emit('close')
    }
  }
}
</script>

<style scoped>
.grade-class-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.grade-class-picker {
  width: 374px;
  height: 371px;
  background: #FFFFFF;
  border-radius: 16px;
  position: relative;
  animation: fadeIn 0.3s ease-out;
  box-shadow: 0px 4px 20px rgba(0, 0, 0, 0.15);
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.picker-header {
  position: absolute;
  top: 1px;
  left: 31px;
  width: 315px;
  height: 58px;
  display: flex;
}

.header-item {
  width: 158px;
  height: 58px;
  background: #FFFFFF;
  display: flex;
  align-items: center;
  justify-content: center;
}

.header-text {
  font-family: Inter;
  font-weight: 400;
  font-size: 16px;
  line-height: 26px;
  color: #000000;
  text-align: center;
}

.header-divider {
  position: absolute;
  top: 59px;
  left: 31px;
  width: 312px;
  height: 1px;
  background: #DEE1E6;
}

.picker-content {
  position: absolute;
  top: 60px;
  left: 31px;
  width: 315px;
  height: 311px;
  display: flex;
  overflow: hidden;
}

.picker-column {
  width: 158px;
  height: 100%;
  overflow-y: auto;
  scroll-behavior: smooth;
}

.picker-column::-webkit-scrollbar {
  width: 0;
}

.picker-item {
  width: 158px;
  height: 52px;
  background: #FFFFFF;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-family: Inter;
  font-weight: 400;
  font-size: 12px;
  line-height: 20px;
  color: #6E7787;
  text-align: center;
  transition: all 0.2s ease;
}

.picker-item:hover {
  background: #F8F9FA;
}

.picker-item.active {
  color: #323842;
  font-weight: 500;
}

/* 不同行的颜色变化 */
.picker-column:first-child .picker-item:nth-child(2),
.picker-column:last-child .picker-item:nth-child(2) {
  color: #565E6C;
}

.picker-column:first-child .picker-item:nth-child(3),
.picker-column:last-child .picker-item:nth-child(3) {
  color: #424955;
}

.picker-column:first-child .picker-item:nth-child(4),
.picker-column:last-child .picker-item:nth-child(4) {
  color: #323842;
}

.picker-column:first-child .picker-item:nth-child(5),
.picker-column:last-child .picker-item:nth-child(5) {
  color: #424955;
}

.picker-column:first-child .picker-item:nth-child(6),
.picker-column:last-child .picker-item:nth-child(6) {
  color: #565E6C;
}
</style>
