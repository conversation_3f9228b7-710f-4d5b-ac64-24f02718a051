<template>
  <div class="change-phone-page">
    <!-- 状态栏背景 -->
    <div class="status-bar"></div>

    <!-- 返回按钮 -->
    <div class="back-button" @click="goBack">
      <img :src="arrowLeftPhone" alt="返回" class="arrow-left" />
    </div>

    <!-- 分隔线 -->
    <div class="separator"></div>

    <!-- 页面标题 -->
    <div class="page-title">更换手机号</div>

    <!-- 原手机号码输入框 -->
    <div class="textbox">
      <div class="textbox-label">原手机号码</div>
      <div class="textbox-divider"></div>
    </div>
    <div class="textbox-placeholder">请填写原手机号码</div>

    <!-- 新手机号码输入框 -->
    <div class="textbox new-phone">
      <div class="textbox-label">新手机号码</div>
      <div class="textbox-divider"></div>
    </div>
    <div class="textbox-placeholder new-phone-placeholder">请填写新手机号码</div>

    <!-- 下一步按钮 -->
    <div class="next-button" @click="nextStep">
      <span>下一步</span>
    </div>
  </div>
</template>

<script>
// 导入图片资源
import arrowLeftPhone from '../assets/images/arrow-left-phone.svg'

export default {
  name: 'ChangePhonePage',
  emits: ['go-back', 'next-step'],
  data() {
    return {
      // 返回按钮图标
      arrowLeftPhone
    }
  },
  methods: {
    goBack() {
      this.$emit('go-back')
    },
    nextStep() {
      this.$emit('next-step')
    }
  }
}
</script>

<style scoped>
.change-phone-page {
  width: 375px;
  height: 844px;
  background: #FFFFFF;
  position: relative;
  margin: 0 auto;
  box-shadow: 0px 3px 6px 0px rgba(18, 15, 40, 0.12);
  font-family: 'Inter', sans-serif;
  overflow-y: auto;
}

/* 状态栏背景 */
.status-bar {
  width: 100%;
  height: 40px;
  background: transparent;
}

/* 返回按钮 */
.back-button {
  position: absolute;
  left: 24px;
  top: 48px;
  width: 24px;
  height: 24px;
  cursor: pointer;
}

.arrow-left {
  width: 100%;
  height: 100%;
}

/* 分隔线 */
.separator {
  position: absolute;
  left: 0;
  top: 80px;
  width: 375px;
  height: 4px;
  background: #F8F9FA;
}

/* 页面标题 */
.page-title {
  position: absolute;
  left: 140px;
  top: 46px;
  width: 80px;
  height: 26px;
  font-family: Inter;
  font-weight: 400;
  font-size: 16px;
  line-height: 26px;
  color: #323842;
}

/* 输入框 */
.textbox {
  position: absolute;
  left: 24px;
  top: 124px;
  width: 327px;
  height: 36px;
  background: #FFFFFF;
  border: 1px solid transparent;
  border-radius: 6px 6px 0px 0px;
  display: flex;
  align-items: center;
  padding: 0 12px;
  box-sizing: border-box;
}

.textbox.new-phone {
  top: 178px;
}

.textbox-label {
  font-family: Inter;
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
  color: #171A1F;
}

.textbox-divider {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 327px;
  height: 1px;
  background: #F3F4F6;
}

.textbox-placeholder {
  position: absolute;
  left: 220px;
  top: 130px;
  width: 112px;
  height: 22px;
  font-family: Inter;
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
  color: #BCC1CA;
}

.textbox-placeholder.new-phone-placeholder {
  top: 185px;
}

/* 下一步按钮 */
.next-button {
  position: absolute;
  left: 13px;
  top: 715px;
  width: 350px;
  height: 52px;
  background: #636AE8;
  border-radius: 26px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0px 4px 9px 0px rgba(99, 106, 232, 0.11), 0px 0px 2px 0px rgba(99, 106, 232, 0.12);
}

.next-button span {
  font-family: Inter;
  font-weight: 400;
  font-size: 18px;
  line-height: 28px;
  color: #FFFFFF;
}
</style>
