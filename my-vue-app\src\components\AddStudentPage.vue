<template>
  <div class="add-student-page">
    <!-- 顶部导航栏 -->
    <div class="header">
      <div class="nav-container">
        <button class="back-btn" @click="goBack">
          <img src="@/assets/images/arrow-left.svg" alt="返回" class="back-icon" />
        </button>
        <div class="header-icons">
          <!-- 左侧图标组 -->
          <div class="icon-group left">
            <!-- 这里可以放置左侧图标 -->
          </div>
          <!-- 右侧图标组 -->
          <div class="icon-group right">
            <!-- 这里可以放置右侧图标 -->
          </div>
        </div>
      </div>
    </div>

    <!-- 分隔线 -->
    <div class="divider"></div>

    <!-- 页面标题 -->
    <h1 class="page-title">添加成员</h1>

    <!-- 头像区域 -->
    <div class="avatar-section">
      <div class="avatar-container">
        <img src="@/assets/images/avatar-student.svg" alt="头像" class="avatar" />
      </div>
    </div>

    <!-- 表单区域 -->
    <div class="form-section">
      <!-- 成员关系 -->
      <div class="form-item relation-item">
        <div class="form-field">
          <label class="field-label">成员关系</label>
          <button class="field-selector" @click="selectRelation">
            <img src="@/assets/images/caret-right.svg" alt="选择" class="caret-icon" />
          </button>
        </div>
        <div class="field-underline"></div>
        <!-- 关系标签 -->
        <div class="relation-tag">
          <span class="tag">{{ formData.relationLabel || '宝贝' }}</span>
        </div>
      </div>

      <!-- 名字 -->
      <div class="form-item">
        <div class="form-field">
          <label class="field-label">名字</label>
          <input 
            type="text" 
            v-model="formData.name" 
            placeholder="请输入" 
            class="field-input"
          />
        </div>
        <div class="field-underline"></div>
      </div>

      <!-- 性别 -->
      <div class="form-item">
        <div class="form-field">
          <label class="field-label">性别</label>
          <span class="field-value" v-if="formData.gender">{{ getGenderText(formData.gender) }}</span>
          <button class="field-selector" @click="selectGender">
            <img src="@/assets/images/caret-right.svg" alt="选择" class="caret-icon" />
          </button>
        </div>
        <div class="field-underline"></div>
      </div>

      <!-- 出生年月 -->
      <div class="form-item">
        <div class="form-field">
          <label class="field-label">出生年月</label>
          <span class="field-value" v-if="formData.birthDate">{{ formData.birthDate }}</span>
          <button class="field-selector" @click="selectBirthDate">
            <img src="@/assets/images/caret-right.svg" alt="选择" class="caret-icon" />
          </button>
        </div>
        <div class="field-underline"></div>
      </div>

      <!-- 就读学校 -->
      <div class="form-item">
        <div class="form-field">
          <label class="field-label">就读学校</label>
          <input 
            type="text" 
            v-model="formData.school" 
            placeholder="请输入" 
            class="field-input"
          />
        </div>
        <div class="field-underline"></div>
      </div>

      <!-- 年级/班级 -->
      <div class="form-item">
        <div class="form-field">
          <label class="field-label">年级/班级</label>
          <span class="field-value" v-if="formData.gradeClass">{{ formData.gradeClass }}</span>
          <button class="field-selector" @click="selectGradeClass">
            <img src="@/assets/images/caret-right.svg" alt="选择" class="caret-icon" />
          </button>
        </div>
        <div class="field-underline"></div>
      </div>

      <!-- 座号 -->
      <div class="form-item">
        <div class="form-field">
          <label class="field-label">座号</label>
          <input 
            type="text" 
            v-model="formData.seatNumber" 
            placeholder="请输入" 
            class="field-input"
          />
        </div>
        <div class="field-underline"></div>
      </div>
    </div>

    <!-- 保存按钮 -->
    <div class="save-section">
      <button class="save-btn" @click="saveStudent">
        保存
      </button>
    </div>

    <!-- 性别选择弹窗 -->
    <GenderSelectionPopup
      v-if="showGenderPopup"
      @close="closeGenderPopup"
      @select-gender="onGenderSelected"
    />

    <!-- 出生年月选择弹窗 -->
    <BirthDatePicker
      v-if="showBirthDatePopup"
      @close="closeBirthDatePopup"
      @select-date="onBirthDateSelected"
    />

    <!-- 年级/班级选择弹窗 -->
    <GradeClassPicker
      v-if="showGradeClassPopup"
      @close="closeGradeClassPopup"
      @select-grade-class="onGradeClassSelected"
    />

    <!-- 成员关系选择弹窗 -->
    <MemberRelationPicker
      v-if="showRelationPopup"
      :current-relation="formData.relation"
      @close="closeRelationPopup"
      @select-relation="onRelationSelected"
    />
  </div>
</template>

<script>
import GenderSelectionPopup from './GenderSelectionPopup.vue'
import BirthDatePicker from './BirthDatePicker.vue'
import GradeClassPicker from './GradeClassPicker.vue'
import MemberRelationPicker from './MemberRelationPicker.vue'

export default {
  name: 'AddStudentPage',
  components: {
    GenderSelectionPopup,
    BirthDatePicker,
    GradeClassPicker,
    MemberRelationPicker
  },
  emits: ['go-back', 'save-student'],
  data() {
    return {
      formData: {
        name: '',
        relation: 'baby',
        relationLabel: '宝贝',
        gender: '',
        birthDate: '',
        school: '',
        gradeClass: '',
        seatNumber: ''
      },
      showGenderPopup: false,
      showBirthDatePopup: false,
      showGradeClassPopup: false,
      showRelationPopup: false
    }
  },
  methods: {
    goBack() {
      this.$emit('go-back')
    },
    selectRelation() {
      this.showRelationPopup = true
    },
    closeRelationPopup() {
      this.showRelationPopup = false
    },
    onRelationSelected(selection) {
      this.formData.relation = selection.id
      this.formData.relationLabel = selection.label
      console.log('选择的成员关系:', selection)
    },
    selectGender() {
      this.showGenderPopup = true
    },
    closeGenderPopup() {
      this.showGenderPopup = false
    },
    onGenderSelected(gender) {
      this.formData.gender = gender
      console.log('选择的性别:', gender)
    },
    getGenderText(gender) {
      return gender === 'male' ? '男' : gender === 'female' ? '女' : ''
    },
    selectBirthDate() {
      this.showBirthDatePopup = true
    },
    closeBirthDatePopup() {
      this.showBirthDatePopup = false
    },
    onBirthDateSelected(date) {
      this.formData.birthDate = date.formatted
      console.log('选择的出生年月:', date)
    },
    selectGradeClass() {
      this.showGradeClassPopup = true
    },
    closeGradeClassPopup() {
      this.showGradeClassPopup = false
    },
    onGradeClassSelected(selection) {
      this.formData.gradeClass = selection.formatted
      console.log('选择的年级/班级:', selection)
    },
    saveStudent() {
      // 保存学生信息逻辑
      console.log('保存学生信息:', this.formData)
      this.$emit('save-student', this.formData)
    }
  }
}
</script>

<style scoped>
.add-student-page {
  width: 375px;
  height: 844px;
  background: #FFFFFF;
  box-shadow: 0px 3px 6px 0px rgba(18, 15, 40, 0.12);
  position: relative;
}

.header {
  width: 100%;
  height: 40px;
  position: relative;
}

.nav-container {
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0);
  display: flex;
  align-items: center;
  position: relative;
}

.back-btn {
  position: absolute;
  left: 24px;
  top: 50%;
  transform: translateY(-50%);
  width: 24px;
  height: 24px;
  background: transparent;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 24px;
  height: 24px;
}

.header-icons {
  width: 100%;
  display: flex;
  justify-content: space-between;
  padding: 0 72px 0 96px;
}

.icon-group {
  display: flex;
  align-items: center;
}

.divider {
  width: 100%;
  height: 4px;
  background: #F8F9FA;
}

.page-title {
  position: absolute;
  left: 156px;
  top: 46px;
  width: 64px;
  height: 26px;
  font-family: Inter;
  font-weight: 400;
  font-size: 16px;
  line-height: 26px;
  color: #323842;
  margin: 0;
}

.avatar-section {
  position: absolute;
  left: 158px;
  top: 101px;
  width: 60px;
  height: 60px;
}

.avatar-container {
  width: 100%;
  height: 100%;
  background: #636AE8;
  border-radius: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar {
  width: 60px;
  height: 60px;
}

.form-section {
  position: absolute;
  top: 189px;
  left: 24px;
  width: 327px;
}

.form-item {
  margin-bottom: 18px;
}

.form-field {
  width: 100%;
  height: 36px;
  background: #FFFFFF;
  border-radius: 6px 6px 0px 0px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 12px;
  position: relative;
}

.field-label {
  font-family: Inter;
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
  color: #171A1F;
}

.field-input {
  border: none;
  outline: none;
  background: transparent;
  font-family: Inter;
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
  color: #171A1F;
  text-align: right;
  flex: 1;
  margin-left: 12px;
}

.field-input::placeholder {
  color: #9095A0;
}

.field-value {
  font-family: Inter;
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
  color: #171A1F;
  margin-left: auto;
  margin-right: 12px;
}

.field-selector {
  background: transparent;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
}

.caret-icon {
  width: 16px;
  height: 16px;
  opacity: 0.6;
  transition: opacity 0.2s ease;
}

.field-selector:hover .caret-icon {
  opacity: 1;
}

.field-underline {
  width: 100%;
  height: 1px;
  background: #F3F4F6;
}

.relation-item {
  position: relative;
}

.relation-tag {
  position: absolute;
  right: 52px;
  top: 4px;
  width: 60px;
  height: 28px;
  z-index: 10;
}

.tag {
  display: inline-block;
  width: 100%;
  height: 100%;
  background: #636AE8;
  border-radius: 14px;
  font-family: Inter;
  font-weight: 400;
  font-size: 12px;
  line-height: 28px;
  text-align: center;
  color: #FFFFFF;
}

.save-section {
  position: absolute;
  left: 13px;
  top: 715px;
  width: 350px;
  height: 52px;
}

.save-btn {
  width: 100%;
  height: 100%;
  background: #636AE8;
  border: none;
  border-radius: 26px;
  box-shadow: 0px 4px 9px 0px rgba(99, 106, 232, 0.11), 0px 0px 2px 0px rgba(99, 106, 232, 0.12);
  font-family: Inter;
  font-weight: 400;
  font-size: 18px;
  line-height: 28px;
  color: #FFFFFF;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.save-btn:hover {
  background: #5a61d9;
}
</style>
